{"buildFiles": ["C:\\Flutter\\flutter\\packages\\flutter_tools\\gradle\\src\\main\\groovy\\CMakeLists.txt"], "cleanCommandsComponents": [["C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "D:\\Flutter\\Works\\dorm\\android\\app\\.cxx\\RelWithDebInfo\\3x2k4j52\\x86", "clean"]], "buildTargetsCommandComponents": ["C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "D:\\Flutter\\Works\\dorm\\android\\app\\.cxx\\RelWithDebInfo\\3x2k4j52\\x86", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}}