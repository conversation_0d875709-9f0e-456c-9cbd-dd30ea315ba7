{"buildFiles": ["C:\\Flutter\\flutter\\packages\\flutter_tools\\gradle\\src\\main\\groovy\\CMakeLists.txt"], "cleanCommandsComponents": [["C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "D:\\Flutter\\Works\\dorm\\android\\app\\.cxx\\Debug\\4z1uc4b3\\armeabi-v7a", "clean"]], "buildTargetsCommandComponents": ["C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "D:\\Flutter\\Works\\dorm\\android\\app\\.cxx\\Debug\\4z1uc4b3\\armeabi-v7a", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}}